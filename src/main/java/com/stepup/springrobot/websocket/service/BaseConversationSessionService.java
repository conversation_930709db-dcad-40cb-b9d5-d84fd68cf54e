package com.stepup.springrobot.websocket.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.chat.*;
import com.stepup.springrobot.model.chat.ConversationLogType;
import com.stepup.springrobot.model.chat.RobotConversationResponseType;
import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.SharedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public abstract class BaseConversationSessionService implements AutoCloseable {
    private final long conversationTimeoutMinutes;

    protected final WebSocketSession session;

    protected final ObjectMapper objectMapper;

    private volatile boolean isPausingRecording = false;

    private ScheduledExecutorService scheduler;

    private ScheduledFuture<?> conversationTimeoutTask;

    private ScheduledFuture<?> pingTask;

    private static final long PING_INTERVAL_SECONDS = 15L;

    protected final AIRobotConversationService aiRobotConversationService;

    private WebSocketClient sttWebSocketClient;

    private final ConcurrentLinkedQueue<byte[]> audioChunkQueue = new ConcurrentLinkedQueue<>();

    private final ConcurrentLinkedQueue<byte[]> storedAudioChunkQueue = new ConcurrentLinkedQueue<>();

    private final String socketHost;

    protected final String userId;

    private final STTHandlerType sttHandlerType;

    protected String language = null;

    protected final String stressTestRobotIds;

    protected AtomicBoolean hasReturnedChatResponse = new AtomicBoolean(false);

    protected final SharedService sharedService;

    private Long conversationId;

    protected BaseConversationSessionService(WebSocketSession session, ObjectMapper objectMapper,
            AIRobotConversationService aiRobotConversationService, long conversationTimeoutMinutes,
            String socketHost, String userId, STTHandlerType sttHandlerType, String stressTestRobotIds, SharedService sharedService) {
        this.session = session;
        this.objectMapper = objectMapper;
        this.aiRobotConversationService = aiRobotConversationService;
        this.conversationTimeoutMinutes = conversationTimeoutMinutes;
        this.socketHost = socketHost;
        this.userId = userId;
        this.sttHandlerType = sttHandlerType;
        this.stressTestRobotIds = stressTestRobotIds;
        this.sharedService = sharedService;
    }

    public void initializeSessionConfig() {
        initializeSTTWebSocketClient();
        scheduler = Executors.newSingleThreadScheduledExecutor();
        startConversationTimeout();
    }

    protected void logInfo(String message) {
        log.info("Client: {} - {} - {}", session.getRemoteAddress(), userId, message);
    }

    protected void logError(Exception e, String message) {
        log.error("Client: {} - {} - {}", session.getRemoteAddress(), userId, message, e);
    }

    protected void initializeSTTWebSocketClient() {
        try {
            String socketEndpoint = socketHost;
            Map<String, String> headers = new HashMap<>();
            if (STTHandlerType.DEEP_GRAM == sttHandlerType) {
                DeepGramModelDTO deepGramModelDTO = aiRobotConversationService.getDeepGramModelInfo();
                socketEndpoint = deepGramModelDTO.getSocketHost();
                headers.put("Authorization", "Token " + deepGramModelDTO.getApiKey());
            }

            String finalSocketEndpoint;
            if (language != null) {
                finalSocketEndpoint = socketEndpoint + "?language=" + language;
            } else {
                finalSocketEndpoint = socketEndpoint;
            }

            Instant start = Instant.now();
            sttWebSocketClient = new WebSocketClient(new URI(finalSocketEndpoint), headers) {
                @Override
                public void onOpen(ServerHandshake handshakedata) {
                    logInfo("(BE - STT) WebSocket connection established: " + finalSocketEndpoint + ", time to connect: " + Duration.between(start, Instant.now()).toMillis());
                }

                @Override
                public void onMessage(String message) {
                    handleOnSocketClientMessage(message);
                }

                @Override
                public void onClose(int code, String reason , boolean remote) {
                    logInfo("(BE - STT) STT WebSocket connection closed. Code: " + code + ", Reason: " + reason + ", Initiated by " + remote);
                }

                @Override
                public void onError(Exception ex) {
                    logError(ex, "(BE - STT) STT WebSocket error: " + finalSocketEndpoint);
                }
            };
            sttWebSocketClient.connect();
        } catch (Exception e) {
            logError(e, "(BE - STT) Error initializing STT WebSocket");
        }
    }

    protected void handleOnSocketClientMessage(String message) {
        try {
            logInfo("(BE - STT) Received STT response: " + message);
            if (isPausingRecording) {
                logInfo("Recording paused, not processing response");
                return;
            }

            String transcript;
            boolean isEndOfSpeech;
            if (STTHandlerType.DEEP_GRAM == sttHandlerType) {
                DeepGramResDTO deepGramResDTO = objectMapper.readValue(message, new TypeReference<>() {
                });
                List<DeepGramAlternativeDTO> alternatives = deepGramResDTO.getChannel().getAlternatives();
                transcript = alternatives.get(alternatives.size() - 1).getTranscript();
                isEndOfSpeech = deepGramResDTO.isSpeechFinal() && !StringUtils.isEmpty(transcript);
            } else {
                // Assume the response is JSON with transcript field
                SpeechToTextDTO speechToTextDTO = objectMapper.readValue(message, new TypeReference<>() {
                });
                List<SpeechToTextSegmentDTO> segments = speechToTextDTO.getSegments();
                transcript = segments.get(segments.size() - 1).getText();
                isEndOfSpeech = speechToTextDTO.isFinal();
            }

            // boolean isEndOfSpeech = false;
            returnASRResult(transcript, message, isEndOfSpeech, null);
        } catch (Exception e) {
            logError(e, "(BE - STT) Error processing STT response" + e.getMessage());
        }
    }

    private void returnASRResult(String transcript, String message, boolean isEndOfSpeech, File audioFile) throws IOException {
        RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.ASR)
                .data(RobotASRMessageDTO.builder()
                        .transcript(transcript)
                        .isStop(isEndOfSpeech)
                        .build())
                .build();

        String asrResponse = objectMapper.writeValueAsString(audioMessage);
        session.sendMessage(new TextMessage(asrResponse));
        //  log.info("(BE - Robot) Send ASR response: {}", asrResponse);

        if (isEndOfSpeech && !StringUtils.isEmpty(transcript)) {
            isPausingRecording = true;
            if (sttWebSocketClient != null) {
                sttWebSocketClient.close();
                sttWebSocketClient = null;
            }

            audioChunkQueue.clear();
            Long botId = getBotIdParam();
            hasReturnedChatResponse.set(false);
            getBotResponse(transcript, message, botId, audioFile);
        }
    }

    protected Long getBotIdParam() {
        Long botId = null;
        if (session.getAttributes().get("bot_id") != null) {
            botId = Long.parseLong(String.valueOf(session.getAttributes().get("bot_id")));
        }

        return botId;
    }

    protected boolean getIsFromWebMvp() {
        boolean isFromWebMvp = false;
        if (session.getAttributes().get("is_web_mvp") != null) {
            isFromWebMvp = Boolean.parseBoolean(String.valueOf(session.getAttributes().get("is_web_mvp")));
        }

        return isFromWebMvp;
    }

    // Start the 5-minute conversation timeout timer
    protected void startConversationTimeout() {
        logInfo("Conversation timer started. The conversation will end in " + conversationTimeoutMinutes + " minutes.");

        conversationTimeoutTask = scheduler.schedule(() -> {
            logInfo("Conversation timeout reached. Ending the conversation.");
            stopConversation();  // End the conversation when the timer expires
        }, conversationTimeoutMinutes, TimeUnit.MINUTES);
    }

    protected void stopConversation() {
        // Close the WebSocket session
        try {
            RobotResponseMessageDTO responseMessageDTO = RobotResponseMessageDTO.builder()
                    .text(CodeDefine.CONVERSATION_FINISH_SENTENCE)
                    .audio(CodeDefine.CONVERSATION_FINISH_AUDIO)
                    .build();
            RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                    .type(RobotConversationResponseType.CHAT_RESPONSE)
                    .data(List.of(responseMessageDTO))
                    .build();
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(audioMessage)));
            session.close(CloseStatus.NORMAL.withReason("Conversation time limit reached."));
            sttWebSocketClient.close();
            isPausingRecording = true;
        } catch (IOException e) {
            log.error("Error closing WebSocket session", e);
            logError(e, "Error closing WebSocket session");
        }

        // Cancel the conversation timeout task if it's still running
        if (conversationTimeoutTask != null && !conversationTimeoutTask.isDone()) {
            conversationTimeoutTask.cancel(true);
            log.info("Conversation timeout task cancelled.");
            logInfo("Conversation timeout task cancelled.");
        }
    }

    public void handleAudioData(ByteBuffer audioData) {
        if (isPausingRecording) {
            return;
        }

        if (sttWebSocketClient == null) {
            initializeSTTWebSocketClient();
        }

        boolean isConvert = getParamIsConvert(session);
        byte[] newData = isConvert
                ? convertAudioFormat(audioData.array())
                : audioData.array();
        // log.info("(Robot - BE) Received audio chunk: {} bytes", newData.length);

        audioChunkQueue.offer(newData.clone());

        storedAudioChunkQueue.offer(newData.clone());

        processQueuedData();
    }

    public void handleTextMessage(String message)  {
        try {
            // First try to parse as ASRTextMessageReqDTO
            try {
                ASRTextMessageReqDTO asrTextMessageReqDTO = objectMapper.readValue(message, ASRTextMessageReqDTO.class);
                if (asrTextMessageReqDTO != null && Objects.equals(asrTextMessageReqDTO.getType(), "ASR")
                    && asrTextMessageReqDTO.getText() != null && asrTextMessageReqDTO.getAudioUrl() != null) {
                    log.info("Received ASR text message: {} with audio URL: {}", asrTextMessageReqDTO.getText(), asrTextMessageReqDTO.getAudioUrl());

                    // Download the audio file from the URL
                    String sessionId = session.getId();
                    String fileName = sessionId + "_" + System.currentTimeMillis() + ".wav";
                    File audioFile = downloadAudioFile(asrTextMessageReqDTO.getAudioUrl(), fileName);
                    returnASRResult(asrTextMessageReqDTO.getText(), message, true, audioFile);
                    return;
                }
            } catch (Exception e) {
                log.error("Error processing ASR text message: {}", e.getMessage(), e);
                // Not an ASRTextMessageReqDTO, continue to try other formats
            }

            // Try to parse as ChatTextMessageReqDTO
            ChatTextMessageReqDTO chatTextMessageReqDTO = objectMapper.readValue(message, new TypeReference<>() {
            });
            if (chatTextMessageReqDTO != null && !StringUtils.isEmpty(chatTextMessageReqDTO.getType())) {
                String type = chatTextMessageReqDTO.getType();
                if (type.equalsIgnoreCase("SKIP")) {
                    returnASRResult("[NEXT]", message, true, null);
                } else if (type.equalsIgnoreCase("BUTTON_LEFT")
                        || type.equalsIgnoreCase("BUTTON_CENTER")
                        || type.equalsIgnoreCase("BUTTON_RIGHT")
                        || type.equalsIgnoreCase("SILENCE")) {
                    returnASRResult(type, message, true, null);
                }
            }
        } catch (Exception e) {
            log.error("Client: {} - {} - {}", session.getRemoteAddress(), userId, message, e);
        }
    }

    protected abstract byte[] convertAudioFormat(byte[] audioData);

    protected void processQueuedData() {
        if (sttWebSocketClient == null || !sttWebSocketClient.isOpen()) {
            return;
        }

        // Safe to poll while new data is being added
        byte[] chunk = audioChunkQueue.poll();
        if (chunk != null) {
            sttWebSocketClient.send(chunk);
            logInfo("(BE - STT) Sent accumulated audio chunk to STT server: " + chunk.length + " bytes");
        }
    }

    public File saveAudio(String sessionId) {
        // Copy the audio data from storedAudioChunkQueue to binaryDataBuffer
        List<byte[]> binaryDataBuffer = new ArrayList<>(storedAudioChunkQueue);

        // Combine all the binary data into one byte array
        byte[] audioData = combineBinaryData(binaryDataBuffer);

        // Convert the byte array to an audio file
        File file = convertToWavFile(audioData, sessionId + "_" + System.currentTimeMillis() + ".wav");

        // Clear the buffer
        binaryDataBuffer.clear();
        storedAudioChunkQueue.clear();

        return file;
    }

    protected byte[] combineBinaryData(List<byte[]> binaryDataBuffer) {
        int totalLength = binaryDataBuffer.stream().mapToInt(data -> data.length).sum();
        byte[] combinedData = new byte[totalLength];
        int offset = 0;
        for (byte[] data : binaryDataBuffer) {
            System.arraycopy(data, 0, combinedData, offset, data.length);
            offset += data.length;
        }

        return combinedData;
    }

    protected abstract File convertToWavFile(byte[] audioData, String fileName);

    /**
     * Downloads an audio file from a URL and saves it locally
     * @param audioUrl The URL of the audio file
     * @param fileName The name to save the file as
     * @return The downloaded file, or null if download failed
     */
    protected File downloadAudioFile(String audioUrl, String fileName) {
        try {
            URL url = new URL(audioUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("Failed to download audio file from {}, response code: {}", audioUrl, responseCode);
                return null;
            }

            // Create the file
            File outputFile = new File(fileName);

            // Download the file content
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(outputFile)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            log.info("Successfully downloaded audio file from {} to {}", audioUrl, fileName);
            return outputFile;
        } catch (Exception e) {
            log.error("Error downloading audio file from {}: {}", audioUrl, e.getMessage(), e);
            return null;
        }
    }

    // This method is now removed as its functionality is merged into getBotResponse

    @Override
    public void close() {
        if (pingTask != null && !pingTask.isDone()) {
            pingTask.cancel(true);
            pingTask = null;
        }
        if (sttWebSocketClient != null) {
            sttWebSocketClient.close();
            sttWebSocketClient = null;
        }

        if (scheduler != null) {
            scheduler.shutdown();
            scheduler = null;
        }

        // Clear queues
        audioChunkQueue.clear();
        storedAudioChunkQueue.clear();
    }

    /**
     * Handle bot response after receiving speech-to-text transcript
     *
     * @param transcript The transcript text
     * @param speechToTextResponse The original speech-to-text response
     * @param botId The bot ID
     * @param providedFile Optional pre-existing audio file (if null, will save audio from the queue)
     */
    protected void getBotResponse(String transcript, String speechToTextResponse, Long botId, File providedFile) {
        Instant start = Instant.now();
        StringBuilder logMessage = new StringBuilder();
        String sessionId = session.getId();

        // Use the provided file or save audio from the queue
        File file = providedFile != null ? providedFile : saveAudio(sessionId);

        // Save user answer with the file name
        if (file != null) {
            aiRobotConversationService.saveUserAnswer(file.getName(), sessionId, transcript, speechToTextResponse);
        }

        // Send stalling message while processing
        CompletableFuture.runAsync(() -> handleSendStalling(logMessage, transcript));

        // Get bot response
        RobotConversationMessageDTO audioMessage = handleGetBotResponse(transcript, file, sessionId, speechToTextResponse, botId, logMessage);

        // Send response back to client
        if (session.isOpen() && audioMessage != null) {
            try {
                String chatResponse = objectMapper.writeValueAsString(audioMessage);
                hasReturnedChatResponse.set(true);
                session.sendMessage(new TextMessage(chatResponse));  // Send the bot response back to the user
                long duration = Duration.between(start, Instant.now()).toMillis();
                sharedService.saveConversationLog(audioMessage.getConversationId(), ConversationLogType.SERVER_RESPONSE, chatResponse, duration);
                logInfo("(BE - Robot) Send bot response - conversation_id : " + audioMessage.getConversationId() + ": " + chatResponse);
                String responseTime = "ROBOT - res time: " + duration + "ms";
                logMessage.insert(0, responseTime);
                log.error(logMessage.toString());
                isPausingRecording = false;
            } catch (IOException e) {
                logError(e, "Failed to send bot response");
            }
        }
    }

    protected void handleSendStalling(StringBuilder logMessage, String userAnswer) {
        Instant startStalling = Instant.now();
        List<RobotResponseMessageDTO> stallingMsgs = aiRobotConversationService.getStallingMessage(session.getId(), userAnswer);
        RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_STALLING)
                .data(stallingMsgs)
                .build();
        try {
            String chatResponse = objectMapper.writeValueAsString(stallingMsg);
            session.sendMessage(new TextMessage(chatResponse));  // Send the bot response back to the user
            logMessage.append(", stalling: ").append(Duration.between(startStalling, Instant.now()).toMillis()).append("ms");
            logInfo("(BE - Robot) Send stalling bot response: " + chatResponse);
        } catch (IOException e) {
            logError(e, "Failed to send bot response");
        }
    }

    protected RobotConversationMessageDTO handleGetBotResponse(String transcript, File file, String sessionId, String speechToTextResponse, Long botId, StringBuilder logMessage) {
        RobotConversationMsgResDTO robotConversationMsgResDTO =  aiRobotConversationService.getRobotResponse(userId, transcript, file, sessionId, speechToTextResponse, session.getRemoteAddress().toString(), botId, logMessage, STTHandlerType.ASR, false);
        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();

        conversationId = resDTOS.get(0).getConversationId();

        return RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(robotConversationMsgResDTO.getResponseMessages())
                .conversationId(resDTOS.get(0).getConversationId())
                .build();
    }

    protected void startPingTask() {
        if (scheduler == null) {
            scheduler = Executors.newSingleThreadScheduledExecutor();
        }

        pingTask = scheduler.scheduleAtFixedRate(() -> {
            RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                    .type(RobotConversationResponseType.PING)
                    .socketSessionId(session.getId())
                    .build();
            try {
                String chatResponse = objectMapper.writeValueAsString(stallingMsg);
                session.sendMessage(new TextMessage(chatResponse));  // Send the bot response back to the user
                logInfo("(BE - Robot) Send ping message: " + chatResponse);
            } catch (IOException e) {
                logError(e, "Failed to send bot response");
            }
        }, 0, PING_INTERVAL_SECONDS, TimeUnit.SECONDS);
    }

    protected boolean getParamIsConvert(WebSocketSession session) {
        Object attribute = session.getAttributes().get("is_convert");
        if (attribute == null) {
            return false;
        }

        return Boolean.parseBoolean(attribute.toString());
    }
}
